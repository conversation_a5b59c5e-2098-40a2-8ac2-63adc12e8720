{"$id": "https://github.com/mikedh/trimesh/blob/main/trimesh/resources/schema/primitive/trimesh.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "additionalProperties": false, "description": "A 3D triangular mesh in the simplest possible JSON format.", "properties": {"faces": {"items": {"items": {"maxItems": 3, "minItems": 3, "type": "integer"}, "type": "array"}, "type": "array"}, "kind": {"pattern": "(^trimesh$)", "type": "string"}, "vertices": {"items": {"items": {"maxItems": 3, "minItems": 3, "type": "number"}, "type": "array"}, "type": "array"}}, "required": ["vertices", "faces"], "type": "object"}