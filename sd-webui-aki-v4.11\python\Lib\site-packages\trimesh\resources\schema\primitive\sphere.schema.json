{"$id": "https://github.com/mikedh/trimesh/blob/main/trimesh/resources/schema/primitive/sphere.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "additionalProperties": false, "description": "A 3D sphere primitive.", "properties": {"kind": {"pattern": "(^sphere$)", "type": "string"}, "radius": {"type": "number"}, "transform": {"$ref": "transform.schema.json"}}, "required": ["radius", "kind"], "type": "object"}