from ... import util
from ...exchange.ply import load_ply
from ...typed import Optional
from ..path import Path
from . import misc
from .dxf import _dxf_loaders
from .svg_io import svg_to_path


def load_path(file_obj, file_type: Optional[str] = None, **kwargs):
    """
    Load a file to a Path file_object.

    Parameters
    -----------
    file_obj
      Accepts many types:
         - Path, Path2D, or Path3D file_objects
         - open file file_object (dxf or svg)
         - file name (dxf or svg)
         - shapely.geometry.Polygon
         - shapely.geometry.MultiLineString
         - dict with kwargs for Path constructor
         - `(n, 2, (2|3)) float` line segments
    file_type
        Type of file is required if file
        object is passed.

    Returns
    ---------
    path : Path, Path2D, Path3D file_object
      Data as a native trimesh Path file_object
    """
    # avoid a circular import
    from ...exchange.load import load_kwargs

    if isinstance(file_type, str):
        # we accept full file names here so make sure we
        file_type = util.split_extension(file_type).lower()

    # record how long we took
    tic = util.now()

    if isinstance(file_obj, Path):
        # we have been passed a file object that is already a loaded
        # trimesh.path.Path object so do nothing and return
        return file_obj
    elif util.is_file(file_obj):
        # for open file file_objects use loaders
        if file_type == "ply":
            # we cannot register this exporter to path_loaders
            # since this is already reserved for 3D values in `trimesh.load`
            kwargs.update(load_ply(file_obj, file_type=file_type))
        else:
            kwargs.update(path_loaders[file_type](file_obj, file_type=file_type))
    elif isinstance(file_obj, str):
        # strings passed are evaluated as file file_objects
        with open(file_obj, "rb") as f:
            # get the file type from the extension
            file_type = util.split_extension(file_obj).lower()
            if file_type == "ply":
                # we cannot register this exporter to path_loaders since this is already reserved by TriMesh in ply format in trimesh.load()
                kwargs.update(load_ply(f, file_type=file_type))
            else:
                # call the loader
                kwargs.update(path_loaders[file_type](f, file_type=file_type))
    elif util.is_instance_named(file_obj, ["Polygon", "MultiPolygon"]):
        # convert from shapely polygons to Path2D
        kwargs.update(misc.polygon_to_path(file_obj))
    elif util.is_instance_named(file_obj, "MultiLineString"):
        # convert from shapely LineStrings to Path2D
        kwargs.update(misc.linestrings_to_path(file_obj))
    elif isinstance(file_obj, dict):
        # load as kwargs
        return load_kwargs(file_obj)
    elif util.is_sequence(file_obj):
        # load as lines in space
        kwargs.update(misc.lines_to_path(file_obj))
    else:
        raise ValueError("Not a supported object type!")

    result = load_kwargs(kwargs)
    util.log.debug(f"loaded {result!s} in {util.now() - tic:0.4f}s")

    return result


def path_formats():
    """
    Get a list of supported path formats.

    Returns
    ------------
    loaders : list of str
        Extensions of loadable formats, ie:
        ['svg', 'dxf']
    """
    return set(path_loaders.keys())


path_loaders = {"svg": svg_to_path}
path_loaders.update(_dxf_loaders)
