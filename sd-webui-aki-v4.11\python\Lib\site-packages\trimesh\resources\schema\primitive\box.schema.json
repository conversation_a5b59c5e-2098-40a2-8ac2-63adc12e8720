{"$id": "https://github.com/mikedh/trimesh/blob/main/trimesh/resources/schema/primitive/box.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "additionalProperties": false, "description": "A 3D box primitive.", "properties": {"extents": {"description": "The length of each side of the 3D box. The center of mass will be at the origin, and the minimum values will be at (-extents / 2) and the maximum values will be at (extents / 2).", "items": {"maxItems": 3, "minItems": 3, "type": "number"}, "type": "array"}, "kind": {"pattern": "(^box$)", "type": "string"}, "transform": {"$ref": "transform.schema.json"}}, "required": ["extents", "kind"], "type": "object"}