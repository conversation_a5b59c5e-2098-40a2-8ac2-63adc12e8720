{"$id": "https://github.com/mikedh/trimesh/blob/main/trimesh/resources/schema/primitive/cylinder.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "additionalProperties": false, "description": "A 3D cylinder primitive.", "properties": {"height": {"description": "The height of the cylinder along Z. The maximum Z value will be at (height / 2) and the minimum will be at (-height / 2)", "type": "number"}, "kind": {"pattern": "(^cylinder$)", "type": "string"}, "radius": {"description": "The radius of the cylinder.", "type": "number"}, "transform": {"$ref": "transform.schema.json"}}, "required": ["radius", "height", "kind"], "type": "object"}