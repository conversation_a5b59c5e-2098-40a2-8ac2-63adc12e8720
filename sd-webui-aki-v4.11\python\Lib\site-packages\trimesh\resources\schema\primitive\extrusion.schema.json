{"$id": "https://github.com/mikedh/trimesh/blob/main/trimesh/resources/schema/primitive/extrusion.schema.json", "$schema": "https://json-schema.org/draft/2020-12/schema", "additionalProperties": false, "description": "A 2D polygon extruded from Z=0 along positive Z by `height` then transformed by the `transform` matrix.", "properties": {"height": {"type": "number"}, "kind": {"pattern": "(^extrusion$)", "type": "string"}, "polygon": {"$ref": "wkt.polygon.schema.json"}, "transform": {"$ref": "transform.schema.json"}}, "required": ["kind", "polygon", "height"], "type": "object"}